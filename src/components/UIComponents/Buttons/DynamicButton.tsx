import React from 'react';
import { Loader2 } from 'lucide-react';
import { LucideIcon } from 'lucide-react';
import { BootstrapTooltip } from '../ToolTip/Tooltip-material-ui';

// Define button variants
const BUTTON_VARIANTS = {
  primary: 'bg-primary text-primary-foreground hover:bg-primary/90 focus:ring-primary disabled:bg-primary/50',
  primaryLegacy: 'bg-primary text-primary-foreground hover:bg-primary/90 focus:ring-primary disabled:bg-primary/50',
  primaryOutline: 'bg-custom-bg-primary text-semantic-gray-500 border border-semantic-gray-300 hover:bg-semantic-gray-200 hover:border-semantic-gray-400 focus:ring-semantic-gray-500',
  orange: 'bg-primary text-primary-foreground hover:bg-primary/90 focus:ring-primary disabled:bg-primary/50',
  orangeOutline: 'bg-custom-bg-primary text-primary border border-primary-300 hover:bg-primary-50 hover:border-primary-400 focus:ring-primary',
  purple: 'bg-semantic-purple-600 text-semantic-purple-50 hover:bg-semantic-purple-700 focus:ring-semantic-gray-300 disabled:bg-semantic-gray-300',
  green: 'bg-success text-success-foreground hover:bg-success/90 focus:ring-semantic-gray-300 disabled:bg-semantic-gray-300',
  secondary: 'bg-custom-bg-primary border border-custom-border text-custom-text-primary hover:bg-semantic-gray-200 focus:ring-semantic-gray-300 disabled:bg-semantic-gray-50',
  square: 'bg-custom-bg-primary border border-custom-border text-custom-text-primary hover:bg-semantic-gray-200 disabled:bg-semantic-gray-50',
  success: 'bg-success text-success-foreground hover:bg-success/90 focus:ring-semantic-gray-300 disabled:bg-semantic-gray-300',
  danger: 'bg-destructive text-destructive-foreground hover:bg-destructive/90 focus:ring-semantic-gray-300 disabled:bg-semantic-gray-300',
  ghost: 'text-semantic-gray-500 hover:text-semantic-gray-700 hover:bg-semantic-gray-200 focus:ring-semantic-gray-300',
  ghostPrimary: 'text-custom-text-primary hover:bg-semantic-gray-200',
  ghostDanger: 'text-custom-text-destructive hover:bg-semantic-gray-200',
  linkPrimary: 'text-semantic-gray-500 hover:bg-semantic-gray-200',
  linkDanger: 'text-destructive hover:bg-semantic-gray-200',
  linkDisable: 'text-semantic-gray-400 bg-semantic-gray-50'
} as const;

// Define button sizes
const BUTTON_SIZES = {
  xsmall: 'px-3 py-1 typography-caption',
  small: 'px-3 py-1.5 typography-body-sm',
  medium: 'px-4 py-1.5 typography-body-sm',
  default: 'px-4 py-2 typography-body-sm max-h-9',
  large: 'px-6 py-3 typography-body',
  sqSmall: 'p-1.5 typography-body-sm',
  sqDefault: 'p-2 typography-body-sm',
} as const;

type ButtonVariant = keyof typeof BUTTON_VARIANTS;
type ButtonSize = keyof typeof BUTTON_SIZES;

interface DynamicButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children?: React.ReactNode;
  variant?: ButtonVariant;
  size?: ButtonSize;
  icon?: LucideIcon;
  text?: string;
  tooltip?: string;
  isLoading?: boolean;
  fullWidth?: boolean;
  className?: string;
  placement?: 'top' | 'bottom' | 'left' | 'right';
  disable?: boolean;
  loading?: boolean;
  responsiveText?: boolean;
}

export const DynamicButton: React.FC<DynamicButtonProps> = ({
  children,
  variant = 'primary',
  size = 'default',
  icon: Icon,
  text = '',
  tooltip,
  isLoading = false,
  disabled = false,
  onClick,
  className = '',
  type = 'button',
  fullWidth = false,
  placement = 'bottom-end',
  disable = false,loading,
  responsiveText = false,
  ...props
}) => {
  const baseStyles = 'inline-flex items-center justify-center gap-2 font-weight-medium transition-all duration-300 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 shadow-sm hover:shadow';

  const buttonClasses = [
    baseStyles,
    BUTTON_VARIANTS[variant],
    BUTTON_SIZES[size],
    fullWidth ? 'w-full' : '',
    (disabled || isLoading || disable) ? 'cursor-not-allowed opacity-50' : '',
    className,
  ].filter(Boolean).join(' ');

  const getLoadingText = (text: React.ReactNode): React.ReactNode => {
    if (typeof text !== 'string') return text;
    return text
      .replace('Submit', 'Submitting...')
      .replace('Delete', 'Deleting...')
      .replace('Create', 'Creating...')
      .replace('Update', 'Updating...');
  };

  return (
    <BootstrapTooltip title={tooltip || ''} placement={placement}>
      <span>
        <button
          type={type}
          onClick={onClick}
          disabled={disabled || loading || disable}
          className={buttonClasses}
          {...props}
        >
          {loading ? (
            <>
              <Loader2 className="size-4 animate-spin" />
              {text && <span className={responsiveText ? 'hidden sm:inline' : ''}>{text.replace("Create", "Creating...")}</span>}
            </>
          ) : (
            <>
              {Icon && <Icon className="size-4 flex-shrink-0" />}
              {text && <span className={`${responsiveText ? 'hidden sm:inline' : ''} whitespace-nowrap`}>{text}</span>}
            </>
          )}
        </button>
      </span>
    </BootstrapTooltip>
  );
};